import openai
import json
import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# 设置 OpenAI API 密钥
openai.api_key = "your-openai-api-key"

# 定义 OpenAI 函数描述
functions = [
    {
        "name": "get_top_cpu_process",
        "description": "Get the process with the highest CPU usage on the local machine",
        "parameters": {
            "type": "object",
            "properties": {},
            "required": []
        }
    }
]

# 连接到 MCP 服务器并调用工具
async def call_mcp_tool(tool_name: str, arguments: dict) -> dict:
    server_params = StdioServerParameters(
        command="python",
        args=["src/mcp_cpu_monitor/server.py"],
        env=None
    )
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            result = await session.call_tool(tool_name, arguments)
            return json.loads(result.content)

# 与 OpenAI API 交互
async def run_conversation(user_input: str) -> dict:
    messages = [{"role": "user", "content": user_input}]
    
    # 调用 OpenAI API
    response = openai.ChatCompletion.create(
        model="gpt-3.5-turbo",
        messages=messages,
        functions=functions,
        function_call="auto"
    )
    
    response_message = response.choices[0].message
    
    # 检查是否需要调用函数
    if response_message.get("function_call"):
        function_name = response_message["function_call"]["name"]
        function_args = json.loads(response_message["function_call"]["arguments"])
        
        if function_name == "get_top_cpu_process":
            result = await call_mcp_tool(function_name, function_args)
            return result
        else:
            return {"status": "error", "error_message": "Unknown function"}
    
    return {"status": "success", "message": response_message["content"]}

# 示例使用
async def main():
    user_input = "Find the process using the most CPU on my computer"
    result = await run_conversation(user_input)
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    asyncio.run(main())
