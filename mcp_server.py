from mcp.server.fastmcp import FastMCP
import psutil
import json

# 初始化 FastMCP 服务器
mcp = FastMCP("cpu-monitor")

# 定义工具：查询当前使用 CPU 最多的进程
@mcp.tool()
def get_top_cpu_process(_: None) -> str:
    """Get the process with the highest CPU usage on the local machine.
    Returns:
        A JSON string containing the process name, PID, and CPU usage percentage.
    """
    try:
        # 获取所有进程并按 CPU 使用率排序
        processes = [(p.info['name'], p.info['pid'], p.cpu_percent(interval=0.1))
                    for p in psutil.process_iter(['name', 'pid'])]
        # 过滤掉 CPU 使用率为 0 的进程
        processes = [p for p in processes if p[2] > 0]
        if not processes:
            return json.dumps({"status": "error", "message": "No active processes found"})
        
        # 找到 CPU 使用率最高的进程
        top_process = max(processes, key=lambda x: x[2])
        return json.dumps({
            "status": "success",
            "name": top_process[0],
            "pid": top_process[1],
            "cpu_percent": top_process[2]
        })
    except Exception as e:
        return json.dumps({"status": "error", "error_message": str(e)})

# 启动服务器
if __name__ == "__main__":
    mcp.run()
